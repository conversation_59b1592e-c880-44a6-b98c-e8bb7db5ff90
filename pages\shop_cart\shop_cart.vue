<template>
  <view class="doctor">
    <cover-view class="nav-bar">
      <uni-status-bar />
      <cover-view class="nav-bar-title">
        <cover-view>中医中华联合会</cover-view>
        <cover-view class="text">携手共同打造</cover-view>
      </cover-view>
    </cover-view>
    <cover-view class="uni-navbar__placeholder">
      <uni-status-bar />
      <cover-view class="uni-navbar__placeholder-view" />
    </cover-view>
	<swiper class="swiper" previous-margin="172rpx" next-margin="172rpx" circular autoplay :interval="1500" :duration="1000">
      <block v-for="(item, index) in swipers" :key="index">
        <swiper-item class="item">
          <image class="thumb" mode="aspectFill" :src="item.image" @tap="navigateTo(`/packageA/doctorDetail/doctorDetail?id=${item.id}`)" />
        </swiper-item>
      </block>
    </swiper>
    <view class="content">
      <image class="ad" v-if="image" mode="widthFix" :src="image" @tap="navigateTo('/packageA/collect/collect')" />
      <view class="user-list">
        <view class="item" v-for="(item, index) in users" :key="index" @tap="navigateTo(`/packageA/report/detail?id=${item.id}`)">
          <view class="con">
            <view class="header">
			  <!-- <image class="avatar" src="https://yangshantong.com/static/images/icon_user1.png"></image> -->
              <view>
                <view>{{item.body_info.name}}</view>
                <view>{{item.create_time}}</view>
              </view>
            </view>
            <view class="con-info">
              <text>舌诊结果:</text>
              <text class="high">{{item.result}}</text>
            </view>
            <view class="con-info">
              <text>健康层级:</text>
              <text class="high" :style="{color: getColor(item.score)}">{{item.score_text}}</text>
            </view>
            <view class="con-info" :style="{width:'100%'}">本次检测结果比对,优胜{{item.ratio || 0}}%年龄人</view>
          </view>
          <u-circle-progress class="progress" :percent="item.score || 0" :width="index == 0 ? 200 : 100" :borderWidth="index == 0 ? 15 : 10" :activeColor="getColor(item.score)" >
            <view class="progress-text">
              <view class="high" :style="{color: getColor(item.score)}">{{item.score || 0}}分</view>
              <view>健康指数</view>
            </view>
          </u-circle-progress>
        </view>
      </view>
	  <view v-if="count > 5" class="more-btn" @tap="navigateTo('/packageA/report/list')">查看更多</view>
		<block  v-for="(item,index) in chartDatas" :key="index">
			<view class="chart-item" v-if="item.scores && item.scores.length > 0">
		  		<view class="chart-name">{{item.name}}</view>
		  		<canvas :canvas-id="'chart'+ item.id" :id="'chart'+ item.id" class="charts" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend"/>
			</view>
		</block>
	  
    </view>
  </view>
</template>

<script>
import { getDockerList,getReportList,getIndexImage,getReportScore } from '@/api/report';
import uCharts from '@/utils/u-charts.min.js';
import dayjs from "@/utils/dayjs.min";
var uChartsInstance = {};
export default {
	data() {
		return {
			  swipers:[],
			  users:[],
			  image:'',
			  cWidth: 710,
			  cHeight: 400,
			  count:0,
			  chartDatas:[],
			};
	},
	components: {},
	computed: {
		getColor() {
		    return (score) => {
				let color = '#f87d80';
				if(score <=65) {
					color = '#f87d80';
				}else if(score > 65 && score <= 75){
					color = '#feb662';
				}else if(score > 75 && score <= 85){
					color = '#ffc704';
				}else if(score > 85 && score <= 90){
					color = '#ffdb63';
				}else{
					color = '#4095e5';
				}
				return color;
		    }
		}
	},
	onReady() {
	    //这里的 750 对应 css .charts 的 width
	    this.cWidth = uni.upx2px(710);
	    //这里的 500 对应 css .charts 的 height
	    this.cHeight = uni.upx2px(400);
	    this.getServerData();
	},
	onShow() {
	  	this.getList();
	  	this.getUserList();
	  	this.getImage();
	},
	methods: {
		async getList(){
			const { code, data, msg } = await getDockerList();
			if(code == 1){
				this.swipers = data;
			}else {
				this.$toast({
					title: msg
				})
			}
		},
		async getUserList(){
			const { code, data, msg } = await getReportList({page_no:'1',page_size:'5'});
			if(code == 1){
				console.log('aa',data);
				this.users = data.list;
				this.count = data.count;
			}else {
				this.$toast({
					title: msg
				})
			}
		},
		async getImage(){
			const { code, data, msg } = await getIndexImage();
			if(code == 1){
				this.image = data.click_image;
			}else {
				this.$toast({
					title: msg
				})
			}
		},
		async getServerData() {
			const { code, data, msg } = await getReportScore();
			if(code != 1){
				this.$toast({
					title: msg
				})
				return;
			};
			this.chartDatas = data.map(v=>{
				return {
					...v,
					categories:v.scores.map(e=>e.create_time),
					series:[
					{
						name: "分数",
						linearColor: [[ 0, "#1890FF" ], [ 0.25, "#00B5FF" ], [ 0.5, "#00D1ED" ],[ 0.75, "#00E6BB" ], [ 1, "#90F489" ]],
						data: v.scores.map(q=>q.score)
					},
				]
				}
			})
			data.forEach((item,index)=>{
				if(item.scores && item.scores.length > 0){
					this.drawCharts('chart'+item.id,index);
				}
			})
	    },
		drawCharts(id,index){
			const ctx = uni.createCanvasContext(id, this);
			uChartsInstance[id] = new uCharts({
				type: "line",
				context: ctx,
				width: this.cWidth,
				height: this.cHeight,
				categories: this.chartDatas[index].categories,
				series: this.chartDatas[index].series,
				animation: true,
				background: "#FFFFFF",
				color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
				padding: this.chartDatas[index].categories.length > 7 ? [23,5,8,15] : [23,5,20,5],
				// dataLabel: false,
				// dataPointShape: false,
				enableScroll: false,
				legend: {show: false},
				fontSize: 13,
				xAxis: {
					disableGrid: true,
					scrollShow: true,
					itemCount: 7,
					fontSize: 8,
					rotateLabel:true,
					rotateAngle:this.chartDatas[index].categories.length > 7 ? 50 : 0
				},
				yAxis: {
				  gridType: "dash",
				  dashLength: 2,
				  disabled:true,
				},
				extra: {
				  line: {
					type: "curve",
					width: 2,
					activeType: "hollow",
					linearType: "custom"
				  }
				}
			});
		},
		touchstart(e){
			uChartsInstance[e.target.id].scrollStart(e);
		},
		touchmove(e){
			uChartsInstance[e.target.id].scroll(e);
		},
		touchend(e){
			uChartsInstance[e.target.id].scrollEnd(e);
			uChartsInstance[e.target.id].touchLegend(e);
			uChartsInstance[e.target.id].showToolTip(e);
		},
		navigateTo(url) {
			uni.navigateTo({ url });
		},
	},
};
</script>

<style lang="scss">
  page{
    // background: #cba43f;
  }
  .doctor{
    .nav-bar{
      position: fixed;
      z-index: 20;
	  background: #cba43f;
      .nav-bar-title{
        display: flex;
        justify-content: center;
        flex-direction: column;
        height: 60px;
        padding: 0 150rpx;
        font-family: SourceHanSerifCN ,PingFang SC;
        color: #ffffff;
        font-size: 30rpx;
        .text{
          margin-top: 5rpx;
          margin-left: 150rpx;
        }
      }
    }
    .uni-navbar__placeholder-view{
      height: 60px;
    }
    .swiper{
      height: 462rpx;
	  padding-top: 15rpx;
      .item{
        padding: 0 8rpx;
      }
      .thumb{
        width: 406rpx;
        height: 462rpx;
        background: #cccccc;
        border-radius: 10rpx;
        // border: 4rpx solid #bd3124;
        box-sizing: border-box;
      }
    }
    .content{
      padding: 15rpx 20rpx;
      .ad{
        display: block;
        width: 100%;
        margin-bottom: 15rpx;
        border-radius: 10rpx;
        // border: 4rpx solid #bd3124;
        box-sizing: border-box;
      }
      .user-list{
        display: flex;
        flex-wrap: wrap;
        .item{
			display: flex;
			position: relative;
			width: 345rpx;
			  // height: 220rpx;
			margin-bottom: 15rpx;
			border:1px solid #cecece;
			border-radius: 10rpx;
			background: #ffffff;
			box-sizing: border-box;
			.con{
				flex: 1;
				padding: 18rpx;
				.header{
				  display: flex;
				  align-items: center;
				  margin-bottom: 20rpx;
				  font-size: 15rpx;
				  line-height: 1.5;
				  .avatar{
					  width: 40rpx;
					  height: 40rpx;
					  margin-right: 8rpx;
					}
				}
				.con-info{
				  width: 215rpx;
				  margin-top: 6rpx;
				  font-size: 20rpx;
				  .high{
					font-weight: 600;
				  }
				}
			}
			.progress{
				position: absolute;
				top: 40rpx;
				right: 10rpx;
			}
			.progress-text{
				font-size: 12rpx;
				text-align: center;
				.high{
				  font-size: 24rpx;
				  color: #4095e5;
				  font-weight: 600;
				}
			}
			&:first-child{
				width: 100%;
				// height: 300rpx;
				.con{
				  padding: 30rpx;
				  .header{
					font-size: 26rpx;
					padding-bottom: 20rpx;
					.avatar{
						width: 60rpx;
						height: 60rpx;
					  margin-right: 20rpx;
					}
				  }
				  .con-info{
					width: 470rpx;
					margin-top: 20rpx;
					font-size: 28rpx;
				  }
				}
				.progress-text{
				  font-size: 28rpx;
				  .high{
					font-size: 40rpx;
				  }
				}
			}
			&:nth-child(even){
				margin-right: 20rpx;
			}
        }
      }
		.more-btn{
			width: 70%;
			height: 80rpx;
			line-height: 80rpx;
			background: #ffffff;
			margin: 0 auto 20rpx;
			font-size: 26rpx;
			text-align: center;
			border: 1px solid #cecece;
			border-radius: 10rpx; 
		}
		.chart-item{
			padding-top: 20rpx;
			.chart-name{
				margin-left: 20rpx;
				margin-bottom: 20rpx;
				font-size: 30rpx;
			}
		}
	  .charts{
		  width: 710rpx;
		  height: 400rpx;
		  margin-bottom: 15rpx;
		  border-radius: 10rpx;
		  background: #ffffff;
		  overflow: hidden;
	  }
    }
  }
</style>
