<template>
	<view class="report-detail">
		<cover-view class="nav-bar">
			<uni-status-bar />
			<cover-view class="nav-bar-con">
				<cover-image class="icon" src="/static/images/icon_arrow_left.png" @tap="goBack"></cover-image>
				<cover-view class="nav-bar-title">
					<cover-view>中医中华联合会</cover-view>
					<cover-view class="text">携手共同打造</cover-view>
			    </cover-view>
		  </cover-view>
		</cover-view>
		<cover-view class="uni-navbar__placeholder">
		  <uni-status-bar />
		  <cover-view class="uni-navbar__placeholder-view" />
		</cover-view>
		<view class="detail-box">
			<view class="seat"></view>
			<view class="user-info">
				<view class="con">
					<view class="header">
						<!-- <image class="avatar" src="https://yangshantong.com/static/images/icon_user1.png"></image> -->
						<view>
							<view>{{detail.body_info.name}}</view>
							<view>{{detail.create_time}}</view>
						</view>
					</view>
					<view class="con-info">
						<text>舌诊结果:</text>
						<text class="high">{{detail.result}}</text>
					</view>
					<view class="con-info">
						<text>健康层级:</text>
						<text class="high" :style="{color: getColor(detail.score)}">{{detail.score_text}}</text>
					</view>
					<view class="con-info" :style="{width:'100%'}">本次检测结果比对,优胜{{detail.ratio || 0}}%年龄人</view>
				</view>
				<u-circle-progress class="progress" :percent="detail.score || 0" :width="200" :borderWidth="15" :activeColor="getColor(detail.score)" >
					<view class="progress-text">
						<view class="high" :style="{color: getColor(detail.score)}">{{detail.score || 0}}分</view>
						<view>健康指数</view>
					</view>
				</u-circle-progress>
			</view>
			<view class="title">主要表现</view>
			<view class="desc">
				<rich-text :nodes="detail.performance"></rich-text>
			</view>
			<view class="title">风险提醒</view>
			<view class="desc">
				<rich-text :nodes="detail.risk"></rich-text>
			</view>
			<view class="tabs">
				<view :class="{on:tanIndex == index,item:true}"  v-for="(item, index) in tabs" :key="index" @tap="changeTabIndex(index)">
					<view class="icon-box">
						<image class="icon" mode="aspectFill" :src="tanIndex == index ? item.focusIcon : item.icon" />
					</view>
					
					<view class="text">{{item.name}}</view>
				</view>
			</view>
			<!-- <view class="tab-con"  v-for="(item,index) in detail[tabs[tanIndex].key]" :key="index">
				<view class="tab-title">
					<view class="line"></view>
					<view>{{item.name}}</view>
				</view>
				<rich-text :nodes="item.content"></rich-text>
			</view> -->
			<view class="tab-con">
				<view class="food" v-if="tanIndex == 0">
					<view class="tab-box item" v-for="(item,index) in detail.food_advices" :key="index">
						<view class="item-top">
							<image class="cover" mode="aspectFill" :src="item.image"></image>
							<view class="name">{{item.name}}</view>
						</view>
						<rich-text class="content" :nodes="item.content"></rich-text>
						<view class="text">健康食材</view>
					</view>
					<view class="tab-box item style1">
						<view class="t-title">
							<view class="line"></view>
							<view>忌口</view>
						</view>
						<scroll-view scroll-x>
							<view class="scroll-imgs">
								<view class="imgs" v-for="(v,i) in detail.un_food_advices" :key="i">
									<image class="cover" mode="aspectFill" :src="v.image"></image>
									<view>{{v.name}}</view>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>
				<view class="cookbooks" v-else-if="tanIndex == 1">
					<view class="tab-box item" v-for="(item,index) in detail.cookbooks" :key="index">
						<view class="t-title">
							<view class="line"></view>
							<view>{{item.name}}</view>
						</view>
						<image class="thumb" mode="aspectFill" :src="item.image"></image>
						<view class="project">
							<view class="project-title">
								<image class="icon" src="/static/images/icon_detail_tab1.png"></image>
								<view>药材</view>
							</view>
							<rich-text :nodes="item.medicine_text"></rich-text>
						</view>
						<view class="project">
							<view class="project-title">
								<image class="icon" src="/static/images/icon_detail_tab1.png"></image>
								<view>食材</view>
							</view>
							<rich-text :nodes="item.food_text"></rich-text>
						</view>
						<view>
							<view class="project-title">
								<image class="icon" src="/static/images/icon_detail_tab2.png"></image>
								<view>烹饪方法</view>
							</view>
							<rich-text :nodes="item.cooking_text"></rich-text>
						</view>
						<view class="effect">
							<view style="color: #224bb4;">#功效：</view>
							<rich-text :nodes="item.effect_text"></rich-text>
						</view>
					</view>
				</view>
				<view class="acupoints" v-else-if="tanIndex == 2">
					<view class="item" v-for="(item,index) in detail.acupoints" :key="index">
						<image class="thumb" mode="aspectFill" :src="item.image"></image>
						<view class="p-con">
							<view class="c-name">{{item.name}}</view>
							<rich-text :nodes="item.synopsis"></rich-text>
						</view>
					</view>
				</view>
				<view class="healthy" v-else-if="tanIndex == 3">
					<view class="tab-box item" v-for="(item,inde) in detail.healthy_exercises" :key="index">
						<view class="t-title">
							<view class="line"></view>
							<view>{{item.name}}</view>
							<view class="label">{{item.label}}</view>
						</view>
						<video class="healthy-video" :src="item.video"></video>
					</view>
				</view>
				<view class="medicines" v-else-if="tanIndex == 4">
					<view class="tab-box item" v-for="(item,inde) in detail.medicines" :key="index">
						<view class="t-title">
							<view class="line"></view>
							<view>{{item.name}}</view>
							<view class="label">{{item.label}}</view>
						</view>
						<rich-text :nodes="item.content"></rich-text>
						<view class="medicines-source">方剂来源：《{{item.source}}》</view>
					</view>
				</view>
				<view class="immunitys" v-else>
					<view class="tab-box item" v-for="(item,inde) in detail.immunitys" :key="index">
						<view class="t-title">
							<view class="line"></view>
							<view>{{item.name}}</view>
						</view>
						<rich-text :nodes="item.content"></rich-text>
					</view>
				</view>
			</view>
		</view>
		
	</view>
</template>

<script>
import { mapMutations } from "vuex";
import { getReportDetail } from '@/api/report';
export default {
	name: "reportDetail",
	  data() {
		return {
			detail:{},
			tabs:[{
				icon:'https://yangshantong.com/static/images/icon_detail.png',
				focusIcon:'https://yangshantong.com/static/images/icon_detail_on1.png',
				name:'饮食建议',
				key:'food_advices'
			},
			{
				icon:'https://yangshantong.com/static/images/icon_detail2.png',
				focusIcon:'https://yangshantong.com/static/images/icon_detail_on2.png',
				name:'膳食建议',
				key:'cookbooks'
			},
			{
				icon:'https://yangshantong.com/static/images/icon_detail3.png',
				focusIcon:'https://yangshantong.com/static/images/icon_detail_on3.png',
				name:'穴位养生',
				key:'acupoints'
			},
			{
				icon:'https://yangshantong.com/static/images/icon_detail4.png',
				focusIcon:'https://yangshantong.com/static/images/icon_detail_on4.png',
				name:'运动建议',
				key:'healthy_exercises'
			},
			{
				icon:'https://yangshantong.com/static/images/icon_detail5.png',
				focusIcon:'https://yangshantong.com/static/images/icon_detail_on5.png',
				name:'中药方剂',
				key:'medicines'
			},
			{
				icon:'https://yangshantong.com/static/images/icon_detail6.png',
				focusIcon:'https://yangshantong.com/static/images/icon_detail_on6.png',
				name:'增强免疫',
				key:'immunitys'
			}],
			tanIndex:0
		};
	},
	async onLoad(options) {
		const { code, data, msg } = await getReportDetail({id:options.id});
		if(code == 1){
			this.detail = data;
		}else {
			this.$toast({
				title: msg
			})
		}
	},
	computed: {
		getColor() {
			return (score) => {
				let color = '#f87d80';
				if(score <=65) {
					color = '#f87d80';
				}else if(score > 65 && score <= 75){
					color = '#feb662';
				}else if(score > 75 && score <= 85){
					color = '#ffc704';
				}else if(score > 85 && score <= 90){
					color = '#ffdb63';
				}else{
					color = '#4095e5';
				}
				return color;
			}
		}
	},
	methods: {
		...mapMutations(["LOGIN"]),
		changeTabIndex(index){
			this.tanIndex = index;
		},
		goBack(){
			uni.navigateBack()
		},
	},
};
</script>

<style lang="scss">
  page {
	  font-family: SourceHanSansSC;
    // background-color: #cba43f;
  }
  .report-detail{
	  width: 100vw;
	  overflow: hidden;
	  padding: 0 20rpx 50rpx;
	  font-family: PingFangSC-Regular,PingFang SC;
		.nav-bar{
			position: fixed;
			left: 0;
			z-index: 20;
			background: #cba43f;
			.nav-bar-con{
				display: flex;
				align-items:center;
				.icon{
					// width: 40rpx;
					// height: 40rpx;
					padding-left: 20rpx;
				}
			}
			.nav-bar-title{
				display: flex;
				justify-content: center;
				flex-direction: column;
				height: 60px;
				padding-left: 130rpx;
				font-family: SourceHanSerifCN ,PingFang SC;
				color: #ffffff;
				font-size: 30rpx;
				.text{
				  margin-top: 5rpx;
				  margin-left: 150rpx;
				}
			}
		}
		.uni-navbar__placeholder-view{
			height: 60px;
		}
		.detail-box{
			position: relative;
			.seat{
				position: absolute;
				top: -20rpx;
				left:-20rpx;
				width: 100vw;
				height: 130rpx;
				background: #cba43f;
			}
		}
		.user-info{
			display: flex;
			position: relative;
			width: 100%;
			margin-top: 20rpx;
			margin-bottom: 15rpx;
			// border:4rpx solid #cecece;
			border-radius: 10rpx;
			background: #ffffff;
			.con{
				flex: 1;
				padding: 18rpx;
				.header{
					display: flex;
					align-items: center;
					font-size: 26rpx;
					padding-bottom: 15rpx;
					line-height: 1.6;
					.avatar{
						width: 60rpx;
						height: 60rpx;
						margin-right: 20rpx;
					}
				}
				.con-info{
					width: 470rpx;
					margin-top: 20rpx;
					font-size: 28rpx;
					.high{
						font-weight: 600;
					}
				}
			}
			.progress{
				position: absolute;
				top: 40rpx;
				right: 10rpx;
			}
			.progress-text{
				font-size: 28rpx;
				text-align: center;
				.high{
					font-size: 40rpx;
					color: #4095e5;
					font-weight: 600;
				}
			}
		}
		.title{
			padding-left: 15rpx;
			padding-bottom: 15rpx;
			font-size: 30rpx;
			font-weight: 600;
		}
		.desc{
			padding: 20rpx;
			margin-bottom: 20rpx;
			// border: 4rpx solid #bd3124;
			background: #fff3f3;
			border-radius: 20rpx;
		}
		.tabs{
			display: flex;
			justify-content: space-between;
			.item{
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				width: 110rpx;
				font-size: 22rpx;
				.icon-box{
					width: 110rpx;
					height: 110rpx;
					display: flex;
					justify-content: center;
					align-items: center;
				}
				.icon{
					width: 100rpx;
					height: 100rpx;
				}
				&.on{
					.icon{
						width: 130rpx;
						height: 130rpx;
					}
					.text{
						font-weight: 600;
						text-shadow: rgb(189, 49, 36) 0px 2px 6px;
					}
				}
			}
		}
		.tab-con{
			margin-top: 30rpx;
			padding: 30rpx 10rpx;
			// background: #f5f6f7;
			// border:1px solid #bd3124;
			border-radius: 20rpx;
			font-size: 28rpx;
			color: #282828;
			rich-text{
				line-height: 35rpx;
				font-size: 24rpx;
			}
			.t-title{
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;
				font-size: 28rpx;
				.line{
					width: 10rpx;
					height: 32rpx;
					background: #8db6ff;
					border-radius: 8rpx;
					margin-right: 14rpx;
				}
				.label{
					padding: 0 10rpx;
					height: 36rpx;
					line-height: 36rpx;
					text-align: center;
					background: #eaf2ff;
					border-radius: 18rpx;
					font-size: 22rpx;
					font-weight: 400;
					color: #0033f3;
					margin-left: 28rpx;
				}
			}
			.tab-box{
				position: relative;
				background: #fafaff;
				box-shadow: 6rpx 8rpx 16rpx 0 rgba(224,230,255,.4);
				padding: 30rpx;
				margin-bottom: 30rpx;
				border-radius: 30rpx;
			}
			.healthy-video{
				width: 100%;
				height: 300rpx;
			}
			.medicines-source{
				font-size: 22rpx;
				font-weight: 400;
				color: #b8b8b8;
				text-align: right;
				margin-top: 32rpx;
			}
			.food{
				.item{
					margin-top: 80rpx;
					&.style1{
						margin-top: 30rpx;
					}
					&:first-child{
						margin-top: 50rpx;
					}
					.item-top{
						display: flex;
						align-items: flex-end;
						margin-top: -80rpx;
						margin-bottom: 20rpx;
						.cover{
							width: 120rpx;
							height: 120rpx;
							margin-right: 20rpx;
							border-radius: 30rpx;
						}
					}
					.text{
						width: 140rpx;
						height: 50rpx;
						position: absolute;
						right: 0;
						top: 20rpx;
						background: #eaf2ff;
						box-shadow: -4rpx 2rpx 4rpx 0 rgba(0,50,196,.2);
						border-radius: 200rpx 0 0 200rpx;
						text-align: center;
						font-size: 24rpx;
						color: #0033f3;
						line-height: 50rpx;
					}
					.scroll-imgs{
						display: flex;
						flex-wrap: nowrap;
						.imgs{
							width: 210rpx;
							margin-right: 30rpx;
							font-size: 24rpx;
							text-align: center;
							.cover{
								width: 180rpx;
								height: 154rpx;
								margin-bottom: 10rpx;
							}
						}
					}
				}
			}
			.cookbooks{
				.item{
					.thumb{
						width: 200rpx;
						height: 140rpx;
						border-radius: 16rpx;
						position: absolute;
						right: 30rpx;
						top: 88rpx;	
					}
					.project{
						width: 390rpx;
						margin-bottom: 40rpx;
					}
					.project-title{
						display: flex;
						align-items: center;
						margin-bottom: 10rpx;
						font-size: 26rpx;
						.icon{
							width: 30rpx;
							height: 30rpx;
						}
					}
					.effect{
						display: flex;
						margin-top: 40rpx;
						color: #4177ff;
						font-size: 24rpx;
						font-weight: 500;
					}
				}
			}
			.acupoints{
				.item{
					display: flex;
					margin-bottom: 30rpx;
					background: #f0f3ff;
					border-radius: 30rpx;
					padding: 30rpx;
					box-shadow: 6rpx 8rpx 16rpx 0 rgba(192,210,255,.4);
					.thumb{
						width: 200rpx;
						height: 200rpx;
						margin-right: 20rpx;
						background: #ffffff;
						border-radius: 16rpx;
					}
					.p-con{
						flex: 1;
						.c-name{
							margin-bottom: 14rpx;
							font-size: 26rpx;
							color: #4177ff;
						}
					}
				}
			}
		}
		.ad{
			display: block;
			width: 100%;
			margin-top: 30rpx;
			margin-bottom: 15rpx;
			border-radius: 10rpx;
			// border: 4rpx solid #bd3124;
		}
	}
</style>
