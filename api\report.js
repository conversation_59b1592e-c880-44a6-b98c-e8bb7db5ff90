import request from '@/utils/request'
import {client} from '@/utils/tools'

//获取医生列表
export function getDockerList(data) {
  return request.get("check_report/doctor_lists", {params: data});
}

//获取医生详情
export function getDockerDetail(data) {
  return request.get("check_report/docker_detail", {params: data});
}

//获取ad图片
export function getIndexImage(data) {
  return request.get("check_report/click_image", {params: data});
}

//获取报告列表
export function getReportList(data) {
  return request.get("check_report/reports", {params: data});
}

//获取报告详情
export function getReportDetail(data) {
  return request.get("check_report/report_detail", {params: data});
}

//获取报告分数
export function getReportScore(data) {
  return request.get("check_report/report_score", {params: data});
}

//获取检测人信息
export function getBodyList(data) {
  return request.get("check_report/body_infos", {params: data});
}

//保存检测人信息
export function saveBodyInfo(data) {
  return request.post("check_report/body_info_save", data);
}

//保存采集信息
export function saveCheckReport(data) {
  return request.post("check_report/check_save", data);
}