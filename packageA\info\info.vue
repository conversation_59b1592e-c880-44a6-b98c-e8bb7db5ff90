<template>
   <form class="info-form" @submit="formSubmit" report-submit="true">
      <view class="form-item">
        <view class="label">姓名</view>
        <view class="line"></view>
        <input class="input" name="name" type="text" placeholder="请输入" placeholder-class="black" />
      </view>
      <view class="form-item">
        <view class="label">手机</view>
        <view class="line"></view>
        <input class="input" name="mobile" type="number" placeholder="请输入" placeholder-class="black" />
      </view>
      <view class="form-item">
        <view class="label">性别</view>
        <view class="line"></view>
        <picker mode="selector" name="gender" class="input" range-key="name" @change="changeSex" :range="sexs" >
          <view>{{sexs[sexIndex].name || '请选择'}}</view>
        </picker>
      </view>
      <view class="form-item">
        <view class="label">年龄</view>
        <view class="line"></view>
        <picker mode="date" name="birthday" class="input" @change="changeAge" >
          <view>{{ageData ? age + '/'+ ageData : '请选择'}}</view>
        </picker>
      </view>
      <view class="form-item">
        <view class="label">身高</view>
        <view class="line"></view>
        <input class="input" name="height" type="text" placeholder="请输入" placeholder-class="black" />
		<view>CM</view>
      </view>
      <view class="form-item">
        <view class="label">体重</view>
        <view class="line"></view>
        <input class="input" name="weight" type="text" placeholder="请输入" placeholder-class="black" />
		<view>KG</view>
      </view>
      <view class="form-item">
        <view class="label">地址</view>
        <view class="line"></view>
        <picker level="city" name="address" mode="region" class="input" @change="changeAddress" >
          <view>{{address ? address : '请选择'}}</view>
        </picker>
      </view>
      <button class="btn" form-type="submit">确认</button>
    </form>
</template>

<script>
import { mapMutations } from "vuex";
import dayjs from "@/utils/dayjs.min";
import { saveBodyInfo } from "@/api/report";
export default {
  name: "info",
  data() {
    return {
      sexs: [{
        name: '男',
		value:'1'
      },
      {
        name: '女',
		value:'2'
      }],
      sexIndex:-1,
	  age:'',
	  ageData:'',
	  address:''
    };
  },
  onLoad() {},
  computed: {
  },
  methods: {
    ...mapMutations(["LOGIN"]),
    changeSex(e){
      this.sexIndex = e.detail.value;
    },
    changeAge(e){
		this.age = dayjs().format('YYYY') - dayjs(e.detail.value).format('YYYY')
		this.ageData = dayjs(e.detail.value).format('YYYY.MM.DD');
    },
    changeAddress(e){
		const value = e.detail.value;
		this.address = value[1];
    },
	async formSubmit(e){
		const values = e.detail.value;
		for (let key in values) {
			if (values[key] === null || values[key] === undefined || values[key] === '' || values[key].length === 0 ) {
				this.$toast({
				  title: "请全部填写",
				});
				return;
			}
		}
		console.log(values);
		const { code, data, msg } = await saveBodyInfo({
			...values,
			gender:this.sexs[this.sexIndex].value,
			address:values.address.join(',')
		});
		if(code == 1){
			this.$toast({
				title: '保存成功'
			});
			uni.navigateBack();
		}else {
			this.$toast({
				title: msg
			})
		}
	}
  },
};
</script>

<style lang="scss">
  page {
    // background-color: #cba43f;
  }
  .info-form{
    display: block;
    padding: 40rpx 30rpx;
    .form-item{
      display: flex;
      align-items: center;
      padding: 0 40rpx;
      height: 80rpx;
      background: #ffffff;
      font-size: 30rpx;
      margin-bottom: 30rpx;
      border-radius: 10rpx;
	  // border: 1px solid #cecece;
      .line{
        width: 2px;
        height: 20rpx;
        margin:  5rpx 40rpx 0;
        background: #000000;
      }
      .input{
        flex: 1;
        font-size: 30rpx;
        text-align: center;
      }
    }
    .btn{
      width: 300rpx;
      margin: 100rpx auto;
      background: #f4ce98;
      line-height: 2;
      font-size: 35rpx;
      color: #a16222;
      font-weight: 600;
      border: 2px solid #a16222;
    }
  }
</style>
