<template>
<view>
<!-- pages/news_list/news_list.wxml -->
<view class="news_list">
    <view class="contain">
        <view class="banner" v-if="showAd">
            <!-- 存在就是10 不存在为7 -->
            <swipers :pid="24" height="480rpx" radius="0rpx"></swipers>
        </view>
        <view class="main"  v-if="showAd">
          <view class="content">
            <view class="goods-list">
              <view class="double">
                <view class="goods-double row-between">
                  <navigator v-for="(item, index) in goodsList" :key="index" class="item bg-white mt20" hover-class="none" open-type="navigate"
                   :url="'/pages/goods_details/goods_details?id=' + item.id">
                   <view class="goods-info">
                    <view class="goods-name line1">{{item.name}}</view>
                    <view class="goods-remark line2">{{item.remark}}</view>
                  </view>
                  <view class="gooods-bottom">
                    <view class="price">
                      <view><price-format color="#FF2C3C"  class="mr10" :first-size="34" :second-size="26" :subscript-size="26" :price="item.price" :weight="500"></price-format></view>
                      <view><price-format class="muted" :firstSize="24" :secondSize="24" :subscript-size="24" line-through :price="item.market_price || item.activity_price"></price-format></view>
                    </view>
                    <view class="goods-img" style="width: 180rpx;height:180rpx;">
                      <custom-image :lazy-load="true" width="180rpx" height="180rpx" radius="10rpx" lazy-load :src="item.image"></custom-image>
                    </view>
                  </view>
                  </navigator>
                </view>
              </view>
            </view>
            <loading-footer :status="status" slotEmpty>
              <view slot="empty" class="column-center" style="padding-top: 100rpx">
                  <image class="img-null" src="https://yangshantong.com/static/images/news_null.png"></image>
                  <text class="nr muted">暂无数据～</text>
              </view>
          </loading-footer>
          </view>
        </view>
    </view>
</view>
<!-- <loading-view v-if="showLoading"></loading-view> -->
</view>
</template>

<script>
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------
import { loadingType } from '@/utils/type';
import {
		trottle,
		loadingFun,
		getRect
	} from '@/utils/tools';
import {
		getGoodsSearch,
	} from '@/api/store';

export default {
  data() {
    return {
      active: 0,
      showLoading: true,
      categoryList: [],
      newsList: [],
      page: 1,
      status: loadingType.LOADING,
      keyword: '',
      goodsList: [],
    	priceSort: '',
			saleSort: '',
	  showAd: false
    };
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
	  this.showAd = true
    this.getGoodsSearchFun();
  },

  onReachBottom: function () {
    this.getGoodsSearchFun();
  },


  methods: {

    async getGoodsSearchFun() {
				let {
					page,
					goodsList,
					keyword,
					priceSort,
					saleSort,
					status
				} = this;
				if (status == loadingType.FINISHED) return;
				const params = {
					category_id: this.type == 1 ? this.id : '',
					brand_id: this.type == 0 ? this.id : '',
          category_id:26,
					page_no: page,
					keyword,
					price: priceSort,
					sales_sum: saleSort
				}
				const data = await loadingFun(getGoodsSearch, page, goodsList, status, params)
				if (!data) return
        console.log(data)
				this.page = data.page
				this.goodsList = data.dataList
				this.status = data.status
			}

  }
};
</script>
<style lang="scss">
/* pages/information/information.wxss */
.news_list {
    .banner {
    
    }
    .main {        
      .content {
        .goods-double {
          flex-wrap: wrap;
          padding: 0 20rpx;
          align-items: stretch;
          .item {
            width: 330rpx;
            border-radius: 10rpx;
            padding: 10rpx;
            .goods-info {
              padding: 10rpx 0;
              .goods-name{
                font-weight: 600;
                font-size: 36rpx;
              }
              .goods-remark{
                margin: 10rpx 0;
                color: #8d8a8a;
                font-size: 28rpx;
              }
            }
            .gooods-bottom{
              display: flex;
              justify-content: space-between;
              padding: 40rpx 0;
              .price{
                padding-top: 110rpx;
              }
              .goods-img{
                
              }
            }
          }
        }
      }
    }
    .footer {
        padding: 30rpx 0;
    }
}

page .van-tabs .van-tab--active {
    color: #333;
	font-size: 34rpx;
}

page .van-tab--active{
	font-weight: 600;
}

.news_list .van-tab {
    width: 25%;
    flex: none;
}

</style>