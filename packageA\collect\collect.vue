<template>
	<view class="collect">
		<view class="nav-bar">
			<uni-status-bar />
			<view class="nav-bar-con">
				<u-icon class="icon" name="arrow-left" color="#ffffff" size="40" @tap="goBack"></u-icon>
				<view class="nav-bar-title">
					<view class="text">姓名:{{userData.name}}</view>
					<view>性别:{{genderText[userData.gender]}}</view>
				</view>
				<view class="btn" @tap="changeUser">
					<text>切换</text>
					<image class="icon-change" src="https://yangshantong.com/static/images/icon_change1.png"></image>
				</view>
			</view>
		</view>
		<view class="uni-navbar__placeholder">
		  <uni-status-bar />
		  <view class="uni-navbar__placeholder-view" />
		</view>
		<view class="camera-box">
			<view class="step-top">
				<view class="step-item" v-for="(item,index) in steps" :key="index">
					<image v-if="index <= stepIndex" class="icon" :src="item.tempImagePath"></image>
					<image v-else class="icon" src="https://yangshantong.com/static/images/icon_phone_add.png"></image>
					<view :class="{on:index <= stepIndex}">{{item.text1}}</view>
				</view>
			</view>
			<block v-if="visible">
				<camera device-position="back" flash="off" binderror="error" :style="'width: 100vw; height: '+cameraHeight + 'px'"></camera>
				<image class="step-img" mode="aspectFill" :style="'width: 100vw; height: '+cameraHeight + 'px'" :src="steps[stepIndex+1].img"></image>
				<view class="step-footer">
					<image class="icon" :src="steps[stepIndex+1].icon"></image>
				</view>
				<view class="camera-btn-box">
					<view>{{steps[stepIndex+1].text}}</view>
					<button class="btn" @tap="takePhoto"></button>
				</view>
			</block>
			<bolck v-else>
				<image v-if="stepIndex == 4" :style="'width: 100vw; height: '+ cameraHeight + 'px'" src="https://yangshantong.com/static/images/shetou_collect_bg.png"></image>
				<view v-else class="submit-text">
					<view>警告！</view>
					<view>请正确收录在本人的检测帐号，以确保数据的连贯性,切勿混入他人数据。缺失与混入数据有可能影响当前和以后的分析结果。</view>
				</view>
			</bolck>
		</view>
	</view>
</template>

<script>
import { mapMutations } from "vuex";
import { uploadFile } from '@/utils/tools';
import { saveCheckReport,getBodyList } from '@/api/report';
import axios from "../../js_sdk/xtshadow-axios/axios.min.js";

export default {
  name: "collect",
  data() {
    return {
		userData:{},
		genderText:{
			0:'无',
			1:'男性', 
			2:'女性'
		},
		steps:[{
			icon:'https://yangshantong.com/static/images/shetou_collect.png',
			img:'https://yangshantong.com/static/images/shetou_collect11.png',
			tempImagePath:'',
			text:'将舌头完整放入框内拍摄',
			text1:'舌面'
		},
		{
			icon:'https://yangshantong.com/static/images/shetou_collect2.png',
			img:'https://yangshantong.com/static/images/shetou_collect22.png',
			tempImagePath:'',
			text:'将舌下脉络完整放入框内拍摄',
			text1:'舌下'
		},
		{
			icon:'https://yangshantong.com/static/images/shetou_collect3.png',
			img:'https://yangshantong.com/static/images/shetou_collect33.png',
			tempImagePath:'',
			text:'将面部完整放入框内拍摄',
			text1:'正脸'
		}],
		stepIndex:-1,
		visible:true,
	};
  },
  onLoad() {
	  const _self = this;
  	uni.$on('changeTester', v => {
		_self.userData = v;
  	});
  },
  onShow(){
	  if(!this.userData.name){
		  this.getList();
	  }
  },
  onUnload() {
  	uni.$off('changeTester');
  },
  computed: {
	  cameraHeight() {
	  	const statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
		const screenHeight = uni.getSystemInfoSync().screenHeight
		// return uni.getSystemInfoSync().safeArea.height - 60; 
		return screenHeight - statusBarHeight - 60;
	  },
	  // imgHeight(){
		 //  const statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
		 //  const screenHeight = uni.getSystemInfoSync().screenHeight
		 //  return screenHeight - statusBarHeight - 60;
	  // }
  },
  methods: {
    ...mapMutations(["LOGIN"]),
	async getList(){
		const { code, data, msg } = await getBodyList();
		if(code == 1){
			if(!!data.length){
				this.userData = data[0];
			}else{
				uni.navigateTo({
					url:'/packageA/info/info'
				})
			}
		}else {
			this.$toast({
				title: msg
			})
		}
	},
	takePhoto(){
		wx.showLoading({
		  title: 'loading......',
		});
		const ctx = wx.createCameraContext();
		ctx.takePhoto({
		  quality: 'high',
		  success: (res) => {
			  wx.hideLoading();
			  this.stepIndex += 1;
			  this.steps[this.stepIndex].tempImagePath = res.tempImagePath;
			  if( this.stepIndex == 2){
				  this.visible = false;
				  uni.showModal({
					title: "",
					content: "采集完成，是否提交检测？",
					confirmText: "提交",
					cancelText: "重拍",
					success: (data) => {
						if (data.confirm) {
							this.save();
						}else if(data.cancel){
							this.stepIndex = -1;
							this.visible = true;
						}
					}
				});
				  
			  }
		  },
		  fail: (res) => {
			  console.log('res',res);
			  wx.hideLoading();
		  },
		})
	},
	async save(){
		wx.showLoading({
		  title: '请稍等，上传中...',
		});
		const files = this.steps.filter(v=>!!v.tempImagePath)
		const results = await Promise.all(files.map(file=>uploadFile(file.tempImagePath)));
		console.log('results',results);
		const { code, data, msg } = await saveCheckReport({body_info_id:this.userData.id,top_image:results[0].url,bottom_image:results[1].url,middle_image:results[2].url});
		if(code == 1){
			this.stepIndex = 4;
			uni.showModal({
				title: "",
				content: "你好，养膳通收到您的检测信息了，结果出来会以短信通知您查看报告。祝您万事通",
				confirmText: "我知道了",
				showCancel:false,
				success: (data) => {
					if (data.confirm) {
						uni.navigateBack()
					}
				}
			});
			
		}else {
			this.$toast({
				title: msg
			})
		}
		
	},
	changeUser(){
		uni.navigateTo({
			url:'/packageA/user/list'
		})
	},
	goBack(){
		uni.navigateBack()
	},
  },
};
</script>

<style lang="scss">
  page {
    background-color: #4f4f4f;
  }
	.collect{
		.nav-bar{
			position: fixed;
			left: 0;
			z-index: 20;
			background: #cba43f;
			.nav-bar-con{
				position: relative;
				display: flex;
				align-items:center;
				height: 60px;
				.icon{
					padding-top: 30rpx;
					padding-left: 20rpx;
					padding-right: 60rpx;
				}
				.nav-bar-title{
					font-size: 28rpx;
					color: #ffffff;
					.text{
						padding-top: 10rpx;
						margin-bottom: 15rpx;
					}
				}
				.btn{
					position: absolute;
					bottom: 20rpx;
					left: 350rpx;
					font-size: 30rpx;
					font-weight: 600;
					color: #347caf;
					.icon-change{
						width: 40rpx;
						height: 40rpx;
						vertical-align: bottom;
					}
				}
			}
		}
		.uni-navbar__placeholder-view{
			height: 60px;
		}
		.camera-box{
			position: relative;
			.step-top{
				display: flex;
				position: absolute;
				top: 10rpx;
				left: 20rpx;
				z-index: 10000000;
				font-size: 26rpx;
				color: #ffffff;
				.step-item{
					margin-right: 20rpx;
					text-align: center;
					.on{
						color: #a2ef4d;
						text-decoration: underline;
					}
				}
				.icon{
					width: 120rpx;
					height: 120rpx;
				}
			}
			.step-img{
				position: absolute;
				top:0;
				left: 0;
			}
			.step-footer{
				position: absolute;
				// bottom: 30rpx;
				bottom: 330rpx;
				left: 40rpx;
				.icon{
					width: 120rpx;
					height: 120rpx;
				}
			}
			.camera-btn-box{
				position: fixed;
				// bottom: 0;
				bottom: 50rpx;
				width: 100%;
				// height: 300rpx;
				color: #ffffff;
				padding: 20rpx 0;
				text-align: center;
				.btn{
					position: relative;
					width: 150rpx;
					height: 150rpx;
					margin: 45rpx auto 0;
					// margin: 0 auto;
					background: #ffffff;
					border-radius: 50%;
					&::after{
						content: "";
						position: absolute;
						top:15rpx;
						left: 15rpx;
						width: 120rpx;
						height: 120rpx;
						background: #65c28b;
						border-radius: 50%;
						transform:scale(1);
					}
				}
			}
		}
		.submit-text{
			position: fixed;
			bottom: 90rpx;
			padding: 0 20rpx;
			color: #ffffff;
			font-size: 26rpx;
			line-height: 1.6;
		}
	}
</style>
