<template>
   <view class="doctor-detail">
    <view class="nav-bar" :style="{background:scrollTop>100 ? '#cba43f' : 'transparent'}">
		<uni-status-bar />
		<view class="nav-bar-con">
			<u-icon class="icon" name="arrow-left" color="#ffffff" size="40" @tap="goBack"></u-icon>
			<view class="nav-bar-title">
				<!-- <view>中医中华联合会</view>
				<view class="text">携手共同打造</view> -->
		    </view>
	  </view>
    </view>
    <!-- <view class="uni-navbar__placeholder">
      <uni-status-bar />
      <view class="uni-navbar__placeholder-view" />
    </view> -->
    <image class="thumb" mode="aspectFill" :src="detail.image" />
    <view class="detail">
		<view class="con">
			<view class="title">简介介绍</view>
			<rich-text :nodes="detail.content"></rich-text>
			<!-- <view class="text">{detail.content}</view>
			<view class="title">简介介绍</view>
			<view class="text">内容</view> -->
		</view>
		<image class="ad" mode="widthFix" :src="image.click_image" @tap="navigateTo('/packageA/collect/collect')" />
		<image class="ad" mode="widthFix" :src="image.customer_image" @tap="previewImage" />
    </view>
   </view>
</template>

<script>
import { mapMutations } from "vuex";
import { getDockerDetail,getIndexImage } from '@/api/report'
export default {
  name: "doctorDetail",
  data() {
    return {
		detail:{},
		image:{},
		scrollTop: 0,
    };
  },
  async onLoad(options) {
		this.getImage();
		const { code, data, msg } = await getDockerDetail({id:options.id});
		if(code == 1){
			this.detail = data;
		}else {
			this.$toast({
				title: msg
			})
		}
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  computed: {
  },
  methods: {
    ...mapMutations(["LOGIN"]),
	async getImage(){
		const { code, data, msg } = await getIndexImage();
		if(code == 1){
			this.image = data;
		}else {
			this.$toast({
				title: msg
			})
		}
	},
	goBack(){
		uni.navigateBack()
	},
	navigateTo(url) {
	  uni.navigateTo({ url });
	},
	previewImage(){
		uni.previewImage({
		  urls: [this.image.customer_image],
		  showmenu:true
		})
	}
  },
};
</script>

<style lang="scss">
  page {
    // background: #cba43f;
  }
  .doctor-detail{
	  padding-bottom: 50rpx;
    .nav-bar{
      position: fixed;
      z-index: 20;
	  background: #cba43f;
	  .nav-bar-con{
		display: flex;
		align-items:center;
		.icon{
			padding-left: 20rpx;
		}
	  }
      .nav-bar-title{
        display: flex;
        justify-content: center;
        flex-direction: column;
        height: 60px;
		padding-left: 130rpx;
		
        // padding: 0 150rpx;
        font-family: SourceHanSerifCN ,PingFang SC;
        color: #ffffff;
        font-size: 30rpx;
        .text{
          margin-top: 5rpx;
          margin-left: 150rpx;
        }
      }
    }
    .uni-navbar__placeholder-view{
      height: 60px;
    }
    .thumb{
		display: block;
      width:100%;
      height:573rpx;
    }
    .detail{
		margin:20rpx;
		.con{
			margin-bottom: 20rpx;
			padding: 30rpx;
			background: #ffffff;
			border-radius: 20rpx;
			font-size: 24rpx;
			// border: 1px solid #bd3124;
			box-shadow: 6rpx 8rpx 16rpx 0 rgba(198,198,198,.4);
			.title{
				// padding-top: 30rpx;
				margin-bottom: 15rpx;
				font-size: 26rpx;
				font-weight: 600;
			}
			.text{
				// padding-bottom: 20rpx;
			}
		}
		.ad{
			display: block;
			width: 100%;
			margin-bottom: 15rpx;
			border-radius: 10rpx;
			// border: 1px solid #bd3124;
		}
	}
  }
</style>
