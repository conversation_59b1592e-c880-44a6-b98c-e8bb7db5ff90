<template>
    <view class="index home-bg" >
		<view class="contain">
			<view class="banner" v-if="showAd">
				<!-- 存在就是10 不存在为7 -->
				<swipers :pid="type ? 10 : 7" height="600rpx" radius="0rpx"></swipers>
			</view>
			<tabs :active="active" @change="changeActive" v-if="categoryList.length">
				<tab v-for="(item, index) in categoryList" :key="index" :title="item.name" ></tab>
			</tabs>
			<view class="main">
				<view class="article-list">
					<view v-for="(item, index) in newsList" :key="index" :data-id="item.id" class="article-item bg-white" @tap="goPage">
						<view class="row">
							<view class="info">
								<view class="title lg line2 mb20">{{ item.title }}</view>
								<view class="lighter line2">
									<view>{{ item.synopsis }}</view>
								</view>
							</view>
							<image width="240rpx" height="180rpx" lazy-load class="img ml20" :src="item.image" />
						</view>
						<view class="row-between mt20">
							<view class="xs muted">发布时间: {{item.create_time}}</view>
							<view class="row">
								<!-- <image class="icon-sm" src="https://yangshantong.com/static/images/icon_see.png"></image> -->
								<view class="ml10 xs muted">{{ item.visit }}人浏览</view>
							</view>
						</view>
					</view>
				</view>
				<loading-footer :status="status" slotEmpty>
					<view slot="empty" class="column-center" style="padding-top: 100rpx">
						<image class="img-null" src="https://yangshantong.com/static/images/news_null.png"></image>
						<text class="nr muted">暂无数据～</text>
					</view>
				</loading-footer>
			</view>
		</view>

        <!-- #ifdef APP-PLUS -->
        <lyg-popup
            v-if="appConfig.app_agreement"
            title="用户使用及隐私保护政策提示"
            protocolPath="/bundle/pages/server_explan/server_explan?type=0"
            policyPath="/bundle/pages/server_explan/server_explan?type=1"
            policyStorageKey="has_read_privacy"
        >
        </lyg-popup>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <!-- 用户隐私协议弹框 -->
        <privacy-popup v-model="showPrivacyPopup"></privacy-popup>
        <!-- #endif -->

        <!-- 无网络提示 -->
        <u-no-network z-index="1200" @retry="handleRetry"></u-no-network>

        <u-back-top
            :scroll-top="scrollTop"
            :top="1000"
            :customStyle="{
                backgroundColor: '#FFF',
                color: '#000',
                boxShadow: '0px 3px 6px rgba(0, 0, 0, 0.1)'
            }"
        ></u-back-top>
    </view>
</template>

<script>
import { mapMutations, mapGetters, mapActions } from 'vuex'
import { getCategoryList, getArticleList } from '@/api/new';
import { loadingType } from '@/utils/type'
import { loadingFun, menuJump, arraySlice, setTabbar, getRect, trottle } from '@/utils/tools'
import { toLogin } from '@/utils/login'
import Cache from '@/utils/cache'
import { getConfig, userShare, getRegisterCoupon } from '@/api/app'
const app = getApp()
export default {
    data() {
        return {
			active: 0,
			showLoading: true,
			categoryList: [],
			newsList: [],
			page: 1,
			status: loadingType.LOADING,
			bannerList: [],
			type: 0,
			showAd: false,
            scrollTop: 0,
            logo: '',
            navHeight: 0,
            status: loadingType.LOADING,
            isDischarge: true,
            enable: true,
            isShowDownload: false,
            showPrivacyPopup: false //微信用户隐私协议
        }
    },
    async onLoad(options) {
        // #ifdef MP-WEIXIN
        if (wx.getPrivacySetting) {
            wx.getPrivacySetting({
                success: (res) => {
                    console.log(res, '------') // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
                    if (res.needAuthorization) {
                        // 需要弹出隐私协议
                        setTimeout(() => {
                            uni.hideTabBar()
                        }, 100)
                        this.showPrivacyPopup = true
                    } else {
                        uni.showTabBar()
                    }
                },
                fail: (err) => {
                    uni.showTabBar()
                    console.log(err)
                }
            })
        } else {
            uni.showTabBar()
        }

        // #endif

       //分类id
       this.id = options.id; //type存在则为帮助中心
       
       this.type = options.type || '';
       
       this.showAd = true
       this.getCategoryListFun();
        this.navHeight = app.globalData.navHeight
        this.isDischarge = false
        console.log(this.appConfig)
        // #ifdef H5
        if (options && options.isapp == 1) {
            this.isShowDownload = true
        }
        // #endif
    },
    async onShow() {
        this.$nextTick(function () {
            getRect('.index').then((res) => {
                if (res.top == 0) {
                    this.navBg = 0
                }
            })
        })

        // #ifdef H5
        this.enable = true
        // #endif
        this.isLogin

        // #ifdef MP
        wx.getUpdateManager().onUpdateReady(function () {
            wx.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success(res) {
                    if (res.confirm) {
                        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                        wx.getUpdateManager().applyUpdate()
                    }
                }
            })
        })
        // #endif
    },
    onHide() {
        // #ifdef H5
        this.enable = false
        // #endif
    },
    onReachBottom() {
		 this.getArticleListFun();
    },
    onPullDownRefresh() {
    },
    onShareAppMessage() {
        console.log(this.inviteCode)
        const shareInfo = Cache.get('shareInfo')
        return {
            title: shareInfo.mnp_share_title,
            path: 'pages/index/index?invite_code=' + this.inviteCode,
            imageUrl: shareInfo.mnp_share_image
        }
    },
    onPageScroll(e) {
        const top = uni.upx2px(100)
        const { scrollTop } = e
        if (!this.enable) return
        let percent = scrollTop / top
        this.navBg = percent > 1 ? 1 : percent
        this.scrollTop = scrollTop
    },
    destroyed() {
        this.isDischarge = true
    },
    methods: {
        ...mapMutations(['SETCONFIG']),
        ...mapActions(['getUser']),
        // 网络重试刷新网络请求（解决在ios中首次进入时需要授权蜂窝加载空白页面问题
        async handleRetry() {
            console.log('网络重试刷')
            try {
                const { code, data } = await getConfig()
                if (code == 1) {
                    this.SETCONFIG(data)
                    setTabbar()
                }
            } catch (e) {
                uni.showTabBar()
            }
            this.getShareInfo()
            this.getUser()
        },
        async getShareInfo() {
            const { code, data } = await userShare()
            if (code == 1) {
                Cache.set('shareInfo', data)
            }
        },
        async tapMenu(item) {
            if (!this.isLogin) return toLogin()
            menuJump(item)
        },
		changeActive(e) {
		  this.active = e;
		  this.page = 1;
		  this.newsList = [];
		  this.status = loadingType.LOADING
		  setTimeout(() => {
		    this.getArticleListFun();
		  }, 100);
		},
		
		getCategoryListFun() {
		  getCategoryList({
		    type: this.type
		  }).then(res => {
		    if (res.code == 1) {
		        this.categoryList = res.data
		        console.log(this.categoryList)
		        this.getArticleListFun();
		    }
		  });
		},
		
		getArticleListFun() {
		  let {
		    active,
		    page,
		    newsList,
		    status
		  } = this;
		  
		  // active是选中的分类索引，索引0是全部，索引1开始才是的分类列表的数据
		  let id = this.categoryList[active].id;
		  
		  loadingFun(getArticleList, page, newsList, status, {
		    type: this.type,
			id,
		    page_no: page}).then(res => {
		        if(res) {
		            this.page = res.page;
		            this.newsList = res.dataList
		            this.status = res.status
		        }
		    })
		},
		goPage(e) {
		  let {
		    id
		  } = e.currentTarget.dataset;
		  uni.navigateTo({
		    url: `/pages/news_details/news_details?id=${id}&type=${this.type}`
		  });
		}
    },
    computed: {
        ...mapGetters(['cartNum', 'inviteCode', 'appConfig']),
        seting() {
            const { index_setting } = this.appConfig
            return index_setting
        }
    }
}
</script>

<style lang="scss">
.index {
   
    .main {        
        .article-list {
            padding-top: 20rpx;
            .article-item {
                padding: 20rpx;
                border-bottom: var(--border);
                align-items: flex-start;
                .info {
                    flex: 1;
                }
                .img {
                    width: 240rpx;
                    height: 180rpx;
                    flex: none;
                }
            }
            &:last-of-type {
                border: none;
            }
        }
    }
    .footer {
        padding: 30rpx 0;
    }
   
   
   page .van-tabs .van-tab--active {
       color: #333;
		font-size: 34rpx;
   }
   
   page .van-tab--active{
   	font-weight: 600;
   }
   
   .van-tab {
       width: 25%;
       flex: none;
   }
   
}
</style>
