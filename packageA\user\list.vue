<template>
	<view class="user-list">
		<view class="nav-bar">
			<uni-status-bar />
			<view class="nav-bar-con">
				<u-icon class="icon" name="arrow-left" color="#ffffff" size="40" @tap="goBack"></u-icon>
				<view class="nav-bar-title">
					<view>中医中华联合会</view>
					<view class="text">携手共同打造</view>
				</view>
		  </view>
		</view>
		<view class="uni-navbar__placeholder">
		  <uni-status-bar />
		  <view class="uni-navbar__placeholder-view" />
		</view>
		<view class="list">
			<view class="item" v-for="(item,index) in list" :key="index" @tap="changeUser(item)">
				<!-- <image class="avatar" mode="aspectFill" src="https://yangshantong.com/static/images/icon_user1.png" /> -->
				<view class="content">
					<view>姓名:{{item.name}}</view>
					<view>性别:<text style="margin-right: 30rpx;">{{genderText[item.gender]}}</text><text>{{getAge(item.birthday)}}岁</text></view>
					<view>注册时间:{{item.create_time}}</view>
					<view>最近检测:{{item.last_time || '' }}</view>
					<view>舌诊结论:<text style="font-weight: 600;">{{item.last_result}}</text></view>
				</view>
			</view>
		</view>
		<view class="btn" @tap="navigateTo('/packageA/info/info')">添加检测人</view>
	</view>
 </template>

<script>
import { mapMutations } from "vuex";
import { getBodyList } from '@/api/report';
import dayjs from "@/utils/dayjs.min";
export default {
  name: "userList",
  data() {
    return {
		list:[],
		genderText:{
			0:'无',
			1:'男性', 
			2:'女性'
		}
	};
  },
  onLoad() {
	  
  },
  onShow(){
	 this.getList(); 
  },
  computed: {
	  getAge() {
	        return (age) => {
				return dayjs().format('YYYY') - dayjs(age).format('YYYY')
	        }
	      }
  },
  methods: {
		async getList(){
			const { code, data, msg } = await getBodyList();
			if(code == 1){
				this.list = data;
			}else {
				this.$toast({
					title: msg
				})
			}
		},
		changeUser(value){
			uni.$emit('changeTester',value);
			uni.navigateBack();
		},
		navigateTo(url) {
		  uni.navigateTo({ url });
		},
		goBack(){
			uni.navigateBack()
		},
  },
};
</script>

<style lang="scss">
  page {
    // background-color: #cba43f;
  }
	.user-list{
		.nav-bar{
			position: fixed;
			left: 0;
			z-index: 20;
			background: #cba43f;
			.nav-bar-con{
				display: flex;
				align-items:center;
				.icon{
					padding-left: 20rpx;
				}
			}
			.nav-bar-title{
				display: flex;
				justify-content: center;
				flex-direction: column;
				height: 60px;
				padding-left: 130rpx;
				font-family: SourceHanSerifCN ,PingFang SC;
				color: #ffffff;
				font-size: 30rpx;
				.text{
				  margin-top: 5rpx;
				  margin-left: 150rpx;
				}
			}
		}
		.uni-navbar__placeholder-view{
			height: 60px;
		}
		.list{
			padding: 30rpx 20rpx;
			.item{
				display: flex;
				align-items: center;
				padding: 20rpx 40rpx;
				margin-bottom: 30rpx;
				background: #ffffff;
				// border: 1px solid #bd3124;
				border-radius: 15rpx;
				.avatar{
					width: 100rpx;
					height: 100rpx;
					margin-right: 60rpx;
					border-radius: 50%;
				}
				.content{
					flex: 1;
					font-size: 26rpx;
					line-height: 1.5;
				}
			}
		}
		.btn{
			position: fixed;
			bottom: 30rpx;
			left: 25rpx;
			width: 700rpx;
			height: 70rpx;
			line-height: 70rpx;
			background: #9b7d31;
			color: #000000;
			font-size: 30rpx;
			text-align: center;
			border-radius: 12rpx;
		}
	}
</style>
