<template>
	<view class="report-list">
		<cover-view class="nav-bar">
			<uni-status-bar />
			<cover-view class="nav-bar-con">
				<cover-image class="icon" src="/static/images/icon_arrow_left.png" @tap="goBack"></cover-image>
				<cover-view class="nav-bar-title">
					<cover-view>中医中华联合会</cover-view>
					<cover-view class="text">携手共同打造</cover-view>
			    </cover-view>
		  </cover-view>
		</cover-view>
		<cover-view class="uni-navbar__placeholder">
		  <uni-status-bar />
		  <cover-view class="uni-navbar__placeholder-view" />
		</cover-view>
		<view class="report-list-box">
			<view class="seat"></view>
			<view class="user-info">
				<view>
					<view v-if="testerData.name" class="mb10">姓名：{{testerData.name}}</view>
					<view v-if="testerData.gender">性别：{{genderText[testerData.gender]}}</view>
					<view v-else>全部</view>
				</view>
				<button class="btn" @tap="navigateTo(`/packageA/user/list`)">选择检测人</button>
			</view>
			<view class="list">
				<view class="item" v-for="(item, index) in list" :key="index" @tap="navigateTo(`/packageA/report/detail?id=${item.id}`)">
					<view class="con">
						<view class="header">
							<!-- <image class="avatar" src="https://yangshantong.com/static/images/icon_user1.png"></image> -->
							<view>
								<view>{{item.body_info.name}}</view>
								<view>{{item.create_time}}</view>
							</view>
						</view>
						<view class="con-info">
							<text>舌诊结果:</text>
							<text class="high">{{item.result}}</text>
						</view>
						<view class="con-info">
							<text>健康层级:</text>
							<text class="high" :style="{color: getColor(item.score)}">{{item.score_text}}</text>
						</view>
						<view class="con-info" :style="{width:'100%'}">本次检测结果比对,优胜{{item.ratio || 0}}%年龄人</view>
					</view>
					<u-circle-progress class="progress" :percent="item.score || 0" :width="200" :borderWidth="15" :activeColor="getColor(item.score)" >
						<view class="progress-text">
							<view class="high" :style="{color: getColor(item.score)}">{{item.score || 0}}分</view>
							<view>健康指数</view>
						</view>
					</u-circle-progress>
				</view>
			</view>
			<view v-if="list.length==0" class="no-data">暂无数据</view>
		</view>
		
	</view>
</template>

<script>
import { mapMutations,mapGetters, mapActions  } from "vuex";
import { getReportList } from '@/api/report';
export default {
	name: "reportList",
	data() {
		return {
			currentPage:1,
			pageSize:10,
			testerData:{},
			genderText:{
				0:'无',
				1:'男', 
				2:'女'
			},
			list:[],
		};
	},
	onLoad() {
		this.refresh();
		uni.$on('changeTester', v => {
			this.testerData = v;
			this.refresh();
		});
	},
	onPullDownRefresh() {
		this.refresh();
	},
	onReachBottom(){
		this.currentPage +=1;
		this.getList();
	},
	onUnload() {
		uni.$off('changeTester');
	},
	computed: {
		...mapGetters(['userInfo']),
		getColor() {
			return (score) => {
				let color = '#f87d80';
				if(score <=65) {
					color = '#f87d80';
				}else if(score > 65 && score <= 75){
					color = '#feb662';
				}else if(score > 75 && score <= 85){
					color = '#ffc704';
				}else if(score > 85 && score <= 90){
					color = '#ffdb63';
				}else{
					color = '#4095e5';
				}
				return color;
			}
		}
	},
	methods: {
		...mapMutations(["LOGIN"]),
		refresh() {
		      this.currentPage = 1;
		      this.list = [];
		      this.getList();
		},
		async getList(){
			const { code, data, msg } = await getReportList({page_no:this.currentPage,page_size:this.pageSize,body_info_id:this.testerData.id});
			if(code == 1){
				this.list = this.list.concat(data.list);
			}else {
				this.$toast({
					title: msg
				})
			}
		},
		navigateTo(url) {
		  uni.navigateTo({ url });
		},
		goBack(){
			uni.navigateBack()
		},
	},
};
</script>

<style lang="scss">
  page {
    // background-color: #cba43f;
  }
	.report-list{
		width: 100vw;
		overflow: hidden;
		padding: 0 20rpx;
		.nav-bar{
			position: fixed;
			left: 0;
			z-index: 20;
			background: #cba43f;
			.nav-bar-con{
				display: flex;
				align-items:center;
				.icon{
					padding-left: 20rpx;
				}
			}
			.nav-bar-title{
				display: flex;
				justify-content: center;
				flex-direction: column;
				height: 60px;
				padding-left: 130rpx;
				font-family: SourceHanSerifCN ,PingFang SC;
				color: #ffffff;
				font-size: 30rpx;
				.text{
				  margin-top: 5rpx;
				  margin-left: 150rpx;
				}
			}
		}
		.uni-navbar__placeholder-view{
			height: 60px;
		}
		.report-list-box{
			position: relative;
			.seat{
				position: absolute;
				top: -30rpx;
				left:-20rpx;
				z-index: -1;
				width: 100vw;
				height: 115rpx;
				background: #cba43f;
			}
		}
		.user-info{
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 40rpx;
			margin-top: 30rpx;
			font-size: 26rpx;
			background: #ffffff;
			border-radius: 20rpx;
			// border: 1px solid #bd3124;
			.mb10{
				margin-bottom: 10rpx;
			}
			.btn{
				padding: 0 20rpx;
				background: #efefef;
				// border: 3rpx solid #6c6c6c;
				font-size: 28rpx;
				color: #0f40f5;
			}
		}
		.list{
			.item{
				display: flex;
				position: relative;
				width: 100%;
				margin-top: 15rpx;
				// border: 1px solid #bd3124;
				border-radius: 10rpx;
				background: #ffffff;
				.con{
					flex: 1;
					padding: 18rpx;
					.header{
						display: flex;
						align-items: center;
						margin-bottom: 20rpx;
						font-size: 26rpx;
						padding-bottom: 20rpx;
						line-height: 1.5;
						.avatar{
							width: 60rpx;
							height: 60rpx;
							margin-right: 20rpx;
						}
					}
					.con-info{
						width: 470rpx;
						margin-top: 20rpx;
						font-size: 28rpx;
						.high{
							font-weight: 600;
						}
					}
				}
				.progress{
					position: absolute;
					top: 50rpx;
					right: 10rpx;
				}
				.progress-text{
					font-size: 28rpx;
					text-align: center;
					.high{
						font-size: 40rpx;
						color: #4095e5;
						font-weight: 600;
					}
				}
			}
		}
		.no-data{
			margin-top: 60rpx;
			color: #747474;
			text-align: center;
		}
	}
</style>
