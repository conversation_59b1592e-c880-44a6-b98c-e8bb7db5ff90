{
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationStyle": "custom",
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "首页",
        "backgroundColorTop": "#FFFFFF",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/activity/activity",
      "style": {
        "navigationStyle": "custom",
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "活动",
        "backgroundColorTop": "#FFFFFF",
        "navigationBarTextStyle": "white"
      }
    },
	{
	  "path": "pages/activity/device",
	  "style": {
	    "navigationStyle": "custom",
	    "enablePullDownRefresh": true,
	    "navigationBarTitleText": "设备",
	    "backgroundColorTop": "#FFFFFF",
	    "navigationBarTextStyle": "white"
	  }
	},
    {
      "path": "pages/address_edit/address_edit",
      "style": {
        "navigationBarTitleText": "收货地址"
      }
    },
    {
      "path": "pages/user_address/user_address",
      "style": {
        "navigationBarTitleText": "收货地址"
      }
    },
    {
      "path": "pages/news_list/news_list",
      "style": {
        "navigationBarTitleText": "商城资讯"
      }
    },
    {
      "path": "pages/news_details/news_details",
      "style": {
        "navigationBarTitleText": "资讯详情"
      }
    },
    {
      "path": "pages/sort/sort",
      "style": {
        "navigationBarTitleText": "分类",
        "disableScroll": true
      }
    },
    {
      "path": "pages/shop_cart/shop_cart",
      "style": {
        "navigationBarTitleText": "检测",
        "navigationStyle": "custom"
      }
    }, {
      "path": "pages/shop_cart/new_shop_cart",
      "style": {
        "navigationBarTitleText": "购物车"
      }
    },
    {
      "path": "pages/user/user",
      "style": {
        "navigationBarTitleText": "个人中心",
        "navigationStyle": "custom",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/goods_search/goods_search",
      "style": {
        "navigationBarTitleText": "商品搜索"
      }
    },
    {
      "path": "pages/goods_details/goods_details",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": "商品详情"
      }
    },
    {
      "path": "pages/confirm_order/confirm_order",
      "style": {
        "navigationBarTitleText": "确认订单"
      }
    },
    {
      "path": "pages/user_collection/user_collection",
      "style": {
        "navigationBarTitleText": "我的收藏"
      }
    },
    {
      "path": "pages/user_coupon/user_coupon",
      "style": {
        "navigationBarTitleText": "我的优惠券"
      }
    },
    {
      "path": "pages/user_getcoupon/user_getcoupon",
      "style": {
        "navigationBarTitleText": "领券中心"
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/forget_pwd/forget_pwd",
      "style": {
        "navigationBarTitleText": "忘记密码",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/register/register",
      "style": {
        "navigationBarTitleText": "注册账号",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/user_order/user_order",
      "style": {
        "navigationBarTitleText": "我的订单",
        "navigationStyle": "custom",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/order_details/order_details",
      "style": {
        "navigationBarTitleText": "订单详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/all_comments/all_comments",
      "style": {
        "navigationBarTitleText": "全部评价"
      }
    },
    {
      "path": "pages/user_vip/user_vip",
      "style": {
        "navigationBarTitleText": "会员中心"
      }
    },
    {
      "path": "pages/pay_result/pay_result",
      "style": {
        "navigationBarTitleText": "支付详情"
      }
    },
    {
      "path": "pages/payment/payment",
      "style": {
        "navigationBarTitleText": "支付订单"
      }
    },
    {
      "path": "pages/webview/webview",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "components/uview-ui/components/u-avatar-cropper/u-avatar-cropper",
      "style": {
        "navigationBarTitleText": "头像裁剪",
        "navigationBarBackgroundColor": "#000000"
      }
    }
  ],
  "subPackages": [
    {
      "root": "bundle",
      // 小程序直播插件

      "pages": [
        {
          "path": "pages/goods_seckill/goods_seckill",
          "style": {
            "navigationBarTitleText": "商品秒杀",
            "navigationStyle": "custom"
          }
        },

        {
          "path": "pages/goods_logistics/goods_logistics",
          "style": {
            "navigationBarTitleText": "物流详情"
          }
        },
        {
          "path": "pages/goods_comment_list/goods_comment_list",
          "style": {
            "navigationBarTitleText": "商品评价列表"
          }
        },
        {
          "path": "pages/goods_reviews/goods_reviews",
          "style": {
            "navigationBarTitleText": "商品评价"
          }
        },
        {
          "path": "pages/activity_detail/activity_detail",
          "style": {}
        },
        {
          "path": "pages/goods_combination/goods_combination",
          "style": {
            "navigationBarTitleText": "拼团活动",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "pages/user_group/user_group",
          "style": {
            "navigationBarTitleText": "我的拼团",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "pages/hot_list/hot_list",
          "style": {
            "navigationBarTitleText": "热销榜单"
          }
        },
        {
          "path": "pages/user_sign/user_sign",
          "style": {
            "navigationBarTitleText": "签到"
          }
        },
        {
          "path": "pages/sign_detail/sign_detail",
          "style": {
            "navigationBarTitleText": "签到详情"
          }
        },
        {
          "path": "pages/sign_rule/sign_rule",
          "style": {
            "navigationBarTitleText": "签到规则"
          }
        },
        {
          "path": "pages/user_spread/user_spread",
          "style": {
            "navigationBarTitleText": "分销推广"
          }
        },
        {
          "path": "pages/invite_fans/invite_fans",
          "style": {
            "navigationBarTitleText": "邀请海报"
          }
        },
        {
          "path": "pages/user_spread_month_bill/user_spread_month_bill",
          "style": {
            "navigationBarTitleText": "月度账单"
          }
        },
        {
          "path": "pages/user_spread_month_bill_detail/user_spread_month_bill_detail",
          "style": {
            "navigationBarTitleText": "月度账单详情"
          }
        },
        {
          "path": "pages/user_spread_order/user_spread_order",
          "style": {
            "navigationBarTitleText": "分销订单"
          }
        },
        {
          "path": "pages/user_fans/user_fans",
          "style": {
            "navigationBarTitleText": "我的粉丝"
          }
        },
        {
          "path": "pages/user_withdraw/user_withdraw",
          "style": {
            "navigationBarTitleText": "提现"
          }
        },
        {
          "path": "pages/user_withdraw_code/user_withdraw_code",
          "style": {
            "navigationBarTitleText": "提现记录"
          }
        },
        {
          "path": "pages/user_wallet/user_wallet",
          "style": {
            "navigationBarTitleText": "我的钱包"
          }
        },
        {
          "path": "pages/user_bill/user_bill",
          "style": {
            "navigationBarTitleText": "钱包明细"
          }
        },
        {
          "path": "pages/user_payment/user_payment",
          "style": {
            "navigationBarTitleText": "用户充值"
          }
        },
        {
          "path": "pages/widthdraw_result/widthdraw_result",
          "style": {
            "navigationBarTitleText": "提现结果"
          }
        },
        {
          "path": "pages/server_explan/server_explan",
          "style": {}
        },
        {
          "path": "pages/message_center/message_center",
          "style": {
            "navigationBarTitleText": "消息中心"
          }
        },
        {
          "path": "pages/notice/notice",
          "style": {
            "navigationBarTitleText": "消息通知"
          }
        },
        {
          "path": "pages/user_profile/user_profile",
          "style": {
            "navigationBarTitleText": "个人设置"
          }
        },
        {
          "path": "pages/user_set/user_set",
          "style": {
            "navigationBarTitleText": "个人资料"
          }
        },
        {
          "path": "pages/post_sale/post_sale",
          "style": {
            "navigationBarTitleText": "退款/售后"
          }
        },
        {
          "path": "pages/apply_refund/apply_refund",
          "style": {
            "navigationBarTitleText": "申请售后"
          }
        },
        {
          "path": "pages/after_sales_detail/after_sales_detail",
          "style": {
            "navigationBarTitleText": "售后详情"
          }
        },
        {
          "path": "pages/input_express_info/input_express_info",
          "style": {
            "navigationBarTitleText": "填写快递单号"
          }
        },
        {
          "path": "pages/contact_offical/contact_offical",
          "style": {
            "navigationBarTitleText": "联系客服"
          }
        },
        {
          "path": "pages/luckly_wheel/luckly_wheel",
          "style": {
            "navigationBarTitleText": "幸运抽奖"
          }
        },
        {
          "path": "pages/prize_record/prize_record",
          "style": {
            "navigationBarTitleText": "抽奖记录"
          }
        },
        {
          "path": "pages/win_prize_code/win_prize_code",
          "style": {
            "navigationBarTitleText": "中奖名单"
          }
        },
        {
          "path": "pages/bargain/bargain",
          "style": {
            "navigationBarTitleText": "砍价活动",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/bargain_process/bargain_process",
          "style": {
            "navigationBarTitleText": "砍价进度",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pages/bargain_code/bargain_code",
          "style": {
            "navigationBarTitleText": "砍价记录"
          }
        },
        {
          "path": "pages/recharge_code/recharge_code",
          "style": {
            "navigationBarTitleText": "充值记录",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "pages/balance_transfer/balance_transfer",
          "style": {
            "navigationBarTitleText": "余额转账"
          }
        },
        {
          "path": "pages/transfer_record/transfer_record",
          "style": {
            "navigationBarTitleText": "转账记录"
          }
        },
        {
          "path": "pages/set_pay_pwd/set_pay_pwd",
          "style": {
            "navigationBarTitleText": "设置转账密码"
          }
        },
        {
          "path": "pages/forget_pay_pwd/forget_pay_pwd",
          "style": {
            "navigationBarTitleText": "忘记转账密码"
          }
        },
        {
          "path": "pages/store_list/store_list",
          "style": {
            "navigationBarTitleText": "门店自提"
          }
        },
        {
          "path": "pages/writeoff_order/writeoff_order",
          "style": {
            "navigationBarTitleText": "核销订单",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "pages/writeoff_detail/writeoff_detail",
          "style": {
            "navigationBarTitleText": "订单结果"
          }
        },
        {
          "path": "pages/license/license",
          "style": {
            "navigationBarTitleText": "资质信息",
            "enablePullDownRefresh": true
          }
        }
      ]
    },
    {
      "root": "packageA",
      "pages": [
        {
          "path": "info/info",
          "style": {
            "navigationBarTitleText": "填写资料",
			"navigationBarTextStyle": "white",
			"navigationBarBackgroundColor": "#cba43f"
          }
        },
        {
          "path": "doctorDetail/doctorDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationStyle": "custom"
          }
        },
		{
			"path": "collect/collect",
			"style": {
			  "navigationBarTitleText": "舌诊采集",
			  "navigationStyle": "custom"
			}
		},
		{
			"path": "report/list",
			"style": {
			  "navigationBarTitleText": "报告列表",
			  "navigationStyle": "custom"
			}
		},
		{
			"path": "report/detail",
			"style": {
			  "navigationBarTitleText": "报告详情",
			  "navigationStyle": "custom"
			}
		},
		{
			"path": "user/list",
			"style": {
			  "navigationBarTitleText": "检测人列表",
			  "navigationStyle": "custom"
			}
		}
      ]
    }
  ],
  "tabBar": {
    "color": "#666",
    "selectedColor": "#FF2C3C",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "/static/images/tab_home.png",
        "selectedIconPath": "/static/images/tab_home_s.png",
        "text": "养生"
      },
      {
        "pagePath": "pages/sort/sort",
        "iconPath": "/static/images/tab_sort.png",
        "selectedIconPath": "/static/images/tab_sort_s.png",
        "text": "膳食"
      },
      {
        "pagePath": "pages/shop_cart/shop_cart",
        "iconPath": "/static/images/tab_cart.png",
        "selectedIconPath": "/static/images/tab_cart_s.png",
        "text": "三通"
      },
      {
        "pagePath": "pages/activity/device",
        "iconPath": "/static/images/tab_acvitity.png",
        "selectedIconPath": "/static/images/tab_acvitity_s.png",
        "text": "活动"
      },
      {
        "pagePath": "pages/user/user",
        "iconPath": "/static/images/tab_user.png",
        "selectedIconPath": "/static/images/tab_user_s.png",
        "text": "我的"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "",
    "navigationBarBackgroundColor": "#FFFFFF",
    "backgroundColor": "#F8F8F8",
    "h5": {
      "navigationStyle": "custom"
    }
  },
  "easycom": {
    "autoscan": true,
    "custom": {
      "^u-(.*)": "@/components/uview-ui/components/u-$1/u-$1.vue"
    }
  }
}
