{
    "name" : "养膳通",
    "appid" : "__UNI__1392352",
    "description" : "",
    "versionName" : "3.0.3",
    "versionCode" : 100,
    "transformPx" : false,
	"sassImplementationName" : "node-sass",
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "runtimeVersion" : "3.3.13", //指定版本  
        "compilerVersion" : "3.3.13", //指定版本  
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持  
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Payment" : {},
            "Share" : {},
            "VideoPlayer" : {},
            "OAuth" : {},
            "FaceID" : {},
            "Geolocation" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:likeshop.yixiangonline.com" ]
                    }
                },
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "您可以设置头像、保存图片到相册，还可以上传图片",
                    "NSPhotoLibraryAddUsageDescription" : "您可以设置头像、保存图片到相册，还可以上传图片",
                    "NSCameraUsageDescription" : "您可以拍照设置头像、拍照上传图片",
                    "NSUserTrackingUsageDescription" : "根据您的习惯为您推荐"
                },
                "idfa" : true,
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "oauth" : {
                    "weixin" : {
                        "appid" : "wxe900e56d8f36b6c0",
                        "appsecret" : "6685551b554315efeb6573d82dd074c3",
                        "UniversalLinks" : "https://likeshop.yixiangonline.com/ulink/"
                    }
                },
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wxe900e56d8f36b6c0",
                        "UniversalLinks" : "https://likeshop.yixiangonline.com/ulink/"
                    },
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wxe900e56d8f36b6c0",
                        "UniversalLinks" : "https://likeshop.yixiangonline.com/ulink/"
                    }
                },
                "ad" : {},
                "push" : {},
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "",
                        "appkey_android" : ""
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "",
                    "xhdpi" : "",
                    "xxhdpi" : "",
                    "xxxhdpi" : ""
                },
                "ios" : {
                    "appstore" : "",
                    "ipad" : {
                        "app" : "",
                        "app@2x" : "",
                        "notification" : "",
                        "notification@2x" : "",
                        "proapp@2x" : "",
                        "settings" : "",
                        "settings@2x" : "",
                        "spotlight" : "",
                        "spotlight@2x" : ""
                    },
                    "iphone" : {
                        "app@2x" : "",
                        "app@3x" : "",
                        "notification@2x" : "",
                        "notification@3x" : "",
                        "settings@2x" : "",
                        "settings@3x" : "",
                        "spotlight@2x" : "",
                        "spotlight@3x" : ""
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common",
                "useOriginalMsgbox" : true
            }
        },
        "uniStatistics" : {
            "enable" : false
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx6a7e6c3ef2932cc4",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "minified" : true
        },
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true
        },
        "requiredPrivateInfos" : [ "getLocation", "chooseAddress" ],
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于小程序位置接口的效果展示"
            }
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "h5" : {
        "title" : "加载中",
        "domain" : "https://b2c.likeshop.cn/mobile/",
        "router" : {
            "base" : "/mobile/",
            "mode" : "history"
        },
        "publicPath" : "/mobile/",
        "devServer" : {
            "https" : false,
            "port" : 8080,
            "proxy" : {
                "/api" : {
                    "target" : "http://likeshop.yixiangonline.com",
                    "changeOrigin" : true,
                    "secure" : false,
                    "pathRewrite" : {
                        "^/api" : "/api/"
                    }
                }
            }
        },
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "6NIBZ-6UGE3-4SM3K-YDO7X-Z5BUV-YBFVJ"
                }
            }
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        }
    }
}
